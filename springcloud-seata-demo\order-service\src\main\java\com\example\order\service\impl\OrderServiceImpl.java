package com.example.order.service.impl;

import com.example.order.entity.Order;
import com.example.order.feign.AccountFeignClient;
import com.example.order.feign.StockFeignClient;
import com.example.order.mapper.OrderMapper;
import com.example.order.service.OrderService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单服务实现类
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private StockFeignClient stockFeignClient;
    
    @Autowired
    private AccountFeignClient accountFeignClient;
    
    /**
     * 创建订单 - 分布式事务
     */
    @Override
    @GlobalTransactional(name = "create-order", rollbackFor = Exception.class)
    public Long createOrder(Order order) {
        log.info("开始创建订单，订单信息：{}", order);
        
        // 计算订单总金额
        BigDecimal totalAmount = order.getPrice().multiply(new BigDecimal(order.getCount()));
        order.setTotalAmount(totalAmount);
        order.setStatus(0); // 创建中
        order.setCreateTime(LocalDateTime.now());
        order.setUpdateTime(LocalDateTime.now());
        
        // 1. 创建订单记录
        log.info("步骤1：创建订单记录");
        orderMapper.insert(order);
        log.info("订单创建成功，订单ID：{}", order.getId());
        
        // 2. 扣减库存
        log.info("步骤2：扣减库存，商品ID：{}，数量：{}", order.getProductId(), order.getCount());
        Boolean stockResult = stockFeignClient.deductStock(order.getProductId(), order.getCount());
        if (!stockResult) {
            log.error("库存扣减失败");
            throw new RuntimeException("库存扣减失败");
        }
        log.info("库存扣减成功");
        
        // 3. 扣减账户余额
        log.info("步骤3：扣减账户余额，用户ID：{}，金额：{}", order.getUserId(), totalAmount);
        Boolean accountResult = accountFeignClient.deductBalance(order.getUserId(), totalAmount);
        if (!accountResult) {
            log.error("账户余额扣减失败");
            throw new RuntimeException("账户余额扣减失败");
        }
        log.info("账户余额扣减成功");
        
        // 4. 更新订单状态为已完成
        log.info("步骤4：更新订单状态为已完成");
        order.setStatus(1); // 已完成
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);
        
        log.info("订单创建完成，订单ID：{}", order.getId());
        return order.getId();
    }

    /**
     * 查询所有订单
     */
    @Override
    public List<Order> getAllOrders() {
        log.info("查询所有订单");
        return orderMapper.selectList(null);
    }

    /**
     * 重置订单数据
     */
    @Override
    public void resetData() {
        log.info("开始重置订单数据");
        // 删除所有订单记录
        orderMapper.delete(null);
        log.info("订单数据重置完成");
    }
}
