package com.example.account.controller;

import com.example.account.entity.Account;
import com.example.account.service.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账户控制器
 */
@Slf4j
@RestController
@RequestMapping("/account")
public class AccountController {
    
    @Autowired
    private AccountService accountService;
    
    /**
     * 扣减账户余额
     */
    @PostMapping("/deduct")
    public Boolean deductBalance(@RequestParam Long userId, @RequestParam BigDecimal amount) {
        log.info("接收到扣减账户余额请求：userId={}, amount={}", userId, amount);
        return accountService.deductBalance(userId, amount);
    }
    
    /**
     * 查询所有账户
     */
    @GetMapping("/list")
    public Map<String, Object> list() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Account> accounts = accountService.getAllAccounts();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", accounts);
        } catch (Exception e) {
            log.error("查询账户列表失败：", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 重置账户数据
     */
    @PostMapping("/reset")
    public Map<String, Object> reset() {
        Map<String, Object> result = new HashMap<>();
        try {
            accountService.resetData();
            result.put("success", true);
            result.put("message", "账户数据重置成功");
            log.info("账户数据重置成功");
        } catch (Exception e) {
            log.error("重置账户数据失败：", e);
            result.put("success", false);
            result.put("message", "重置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "account-service");
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
