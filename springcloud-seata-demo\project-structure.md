# 项目结构详细说明

## 📁 完整目录结构

```
springcloud-seata-demo/
├── pom.xml                                    # 父项目POM文件
├── README.md                                  # 项目说明文档
├── project-structure.md                      # 项目结构说明
├── start-services.bat                        # Windows启动脚本
├── start-services.sh                         # Linux/Mac启动脚本
├── test-api.http                             # API测试文件
├── sql/
│   └── init.sql                              # 数据库初始化脚本
├── logs/                                     # 日志目录（运行时创建）
├── order-service/                            # 订单服务
│   ├── pom.xml
│   └── src/
│       ├── main/
│       │   ├── java/com/example/order/
│       │   │   ├── OrderServiceApplication.java      # 启动类
│       │   │   ├── controller/
│       │   │   │   └── OrderController.java          # 订单控制器
│       │   │   ├── service/
│       │   │   │   ├── OrderService.java             # 订单服务接口
│       │   │   │   └── impl/
│       │   │   │       └── OrderServiceImpl.java     # 订单服务实现
│       │   │   ├── entity/
│       │   │   │   └── Order.java                    # 订单实体
│       │   │   ├── mapper/
│       │   │   │   └── OrderMapper.java              # 订单Mapper
│       │   │   └── feign/
│       │   │       ├── StockFeignClient.java         # 库存服务客户端
│       │   │       └── AccountFeignClient.java       # 账户服务客户端
│       │   └── resources/
│       │       └── application.yml                   # 配置文件
│       └── test/                                     # 测试目录
├── stock-service/                            # 库存服务
│   ├── pom.xml
│   └── src/
│       ├── main/
│       │   ├── java/com/example/stock/
│       │   │   ├── StockServiceApplication.java      # 启动类
│       │   │   ├── controller/
│       │   │   │   └── StockController.java          # 库存控制器
│       │   │   ├── service/
│       │   │   │   ├── StockService.java             # 库存服务接口
│       │   │   │   └── impl/
│       │   │   │       └── StockServiceImpl.java     # 库存服务实现
│       │   │   ├── entity/
│       │   │   │   └── Stock.java                    # 库存实体
│       │   │   └── mapper/
│       │   │       └── StockMapper.java              # 库存Mapper
│       │   └── resources/
│       │       └── application.yml                   # 配置文件
│       └── test/                                     # 测试目录
└── account-service/                          # 账户服务
    ├── pom.xml
    └── src/
        ├── main/
        │   ├── java/com/example/account/
        │   │   ├── AccountServiceApplication.java    # 启动类
        │   │   ├── controller/
        │   │   │   └── AccountController.java        # 账户控制器
        │   │   ├── service/
        │   │   │   ├── AccountService.java           # 账户服务接口
        │   │   │   └── impl/
        │   │   │       └── AccountServiceImpl.java   # 账户服务实现
        │   │   ├── entity/
        │   │   │   └── Account.java                  # 账户实体
        │   │   └── mapper/
        │   │       └── AccountMapper.java            # 账户Mapper
        │   └── resources/
        │       └── application.yml                   # 配置文件
        └── test/                                     # 测试目录
```

## 🔧 核心组件说明

### 1. 父项目 (springcloud-seata-demo)
- **作用**: 统一管理依赖版本和构建配置
- **关键配置**: Spring Boot、Spring Cloud、Seata版本管理

### 2. 订单服务 (order-service)
- **端口**: 8081
- **数据库**: seata_order
- **职责**: 
  - 创建订单记录
  - 协调分布式事务
  - 调用库存和账户服务
- **关键注解**: @GlobalTransactional

### 3. 库存服务 (stock-service)
- **端口**: 8082
- **数据库**: seata_stock
- **职责**: 
  - 管理商品库存
  - 扣减库存操作
  - 参与分布式事务

### 4. 账户服务 (account-service)
- **端口**: 8083
- **数据库**: seata_account
- **职责**: 
  - 管理用户账户
  - 扣减账户余额
  - 参与分布式事务

## 🌐 服务间调用关系

```
订单服务 (8081)
    ├── 调用库存服务 (8082) - 扣减库存
    └── 调用账户服务 (8083) - 扣减余额
```

## 📊 数据库设计

### 业务数据库
- **seata_order**: 订单相关数据
- **seata_stock**: 库存相关数据
- **seata_account**: 账户相关数据

### Seata相关表
- **undo_log**: 每个业务数据库都有，用于事务回滚
- **global_table**: Seata服务器数据库，全局事务记录
- **branch_table**: Seata服务器数据库，分支事务记录
- **lock_table**: Seata服务器数据库，全局锁记录

## 🚀 启动顺序

1. **Seata服务器** (已启动在114.132.181.98:8091)
2. **MySQL数据库** (已启动在114.132.181.98:3306)
3. **订单服务** (8081)
4. **库存服务** (8082)
5. **账户服务** (8083)

## 🔄 事务流程

### 正常流程
1. 客户端调用订单服务创建订单
2. 订单服务开启全局事务
3. 订单服务创建订单记录
4. 订单服务调用库存服务扣减库存
5. 订单服务调用账户服务扣减余额
6. 订单服务更新订单状态
7. 全局事务提交

### 异常回滚
1. 任何步骤发生异常
2. Seata检测到异常
3. 自动回滚所有已执行的操作
4. 恢复数据到事务开始前的状态

## 📝 配置要点

### 关键配置项
- **tx-service-group**: 事务服务组名称
- **vgroup-mapping**: 虚拟组到物理组的映射
- **grouplist**: Seata服务器地址列表

### 数据源配置
- 每个服务连接不同的业务数据库
- 统一连接到同一个Seata服务器

## 🎯 扩展点

### 1. 新增微服务
- 复制现有服务结构
- 修改包名和配置
- 添加业务逻辑
- 创建对应数据库和表

### 2. 集成注册中心
- 添加Nacos依赖
- 修改Seata注册中心配置
- 服务间调用改为服务发现

### 3. 配置中心
- 将配置文件迁移到Nacos
- 实现配置动态刷新

这个项目结构清晰，易于理解和扩展，是学习Spring Cloud和Seata分布式事务的理想示例。
