// 模拟数据，用于测试前端功能
const mockData = {
    stocks: [
        {
            id: 1,
            productId: 1,
            productName: "iPhone 15 Pro",
            total: 100,
            used: 5,
            residue: 95
        },
        {
            id: 2,
            productId: 2,
            productName: "MacBook Pro",
            total: 50,
            used: 2,
            residue: 48
        },
        {
            id: 3,
            productId: 3,
            productName: "iPad Air",
            total: 80,
            used: 0,
            residue: 80
        }
    ],
    accounts: [
        {
            id: 1,
            userId: 1,
            userName: "张三",
            total: 100000.00,
            used: 44995.00,
            residue: 55005.00
        },
        {
            id: 2,
            userId: 2,
            userName: "李四",
            total: 50000.00,
            used: 31998.00,
            residue: 18002.00
        },
        {
            id: 3,
            userId: 3,
            userName: "王五",
            total: 30000.00,
            used: 0.00,
            residue: 30000.00
        }
    ],
    orders: [
        {
            id: 1,
            userId: 1,
            productId: 1,
            count: 2,
            price: 8999.00,
            totalAmount: 17998.00,
            status: 1,
            createTime: "2025-07-29T10:30:00"
        },
        {
            id: 2,
            userId: 1,
            productId: 1,
            count: 3,
            price: 8999.00,
            totalAmount: 26997.00,
            status: 1,
            createTime: "2025-07-29T11:15:00"
        },
        {
            id: 3,
            userId: 2,
            productId: 2,
            count: 2,
            price: 15999.00,
            totalAmount: 31998.00,
            status: 1,
            createTime: "2025-07-29T12:00:00"
        }
    ]
};

// 模拟API响应
function getMockStockData() {
    return {
        success: true,
        message: "查询成功",
        data: mockData.stocks
    };
}

function getMockAccountData() {
    return {
        success: true,
        message: "查询成功",
        data: mockData.accounts
    };
}

function getMockOrderData() {
    return {
        success: true,
        message: "查询成功",
        data: mockData.orders
    };
}

function getMockResetResponse() {
    return {
        success: true,
        message: "重置成功"
    };
}
