# 故障排除指南

## 🚨 常见问题及解决方案

### 1. 服务启动问题

#### 问题：服务启动失败，端口被占用
**错误信息**:
```
Port 8081 was already in use
```

**解决方案**:
```bash
# 查看端口占用情况
netstat -tulpn | grep 8081
# 或者
lsof -i :8081

# 杀死占用端口的进程
kill -9 <PID>

# 或者修改配置文件中的端口
# application.yml
server:
  port: 8084  # 修改为其他端口
```

#### 问题：Java版本不兼容
**错误信息**:
```
Unsupported major.minor version 52.0
```

**解决方案**:
```bash
# 检查Java版本
java -version

# 确保使用JDK 8+
# 如果版本过低，请升级Java版本
```

#### 问题：Maven依赖下载失败
**错误信息**:
```
Could not resolve dependencies
```

**解决方案**:
```bash
# 清理Maven缓存
mvn clean

# 强制更新依赖
mvn clean compile -U

# 检查Maven配置
mvn -version

# 配置阿里云镜像（如果网络问题）
# 在 ~/.m2/settings.xml 中添加：
<mirrors>
  <mirror>
    <id>aliyun</id>
    <mirrorOf>central</mirrorOf>
    <url>https://maven.aliyun.com/repository/central</url>
  </mirror>
</mirrors>
```

### 2. 数据库连接问题

#### 问题：数据库连接失败
**错误信息**:
```
Communications link failure
```

**解决方案**:
```bash
# 1. 检查数据库是否运行
mysql -h ************ -P 32775 -u root -p123456 -e "SELECT 1"

# 2. 检查网络连接
ping ************
telnet ************ 32775

# 3. 检查防火墙设置
# 确保端口32775已开放

# 4. 检查数据库配置
# application.yml中的数据库连接信息是否正确
```

#### 问题：数据库表不存在
**错误信息**:
```
Table 'seata_order.t_order' doesn't exist
```

**解决方案**:
```bash
# 执行数据库初始化脚本
mysql -h ************ -P 32775 -u root -p123456 < sql/init.sql

# 验证表是否创建成功
mysql -h ************ -P 32775 -u root -p123456 -e "USE seata_order; SHOW TABLES;"
```

#### 问题：Seata undo_log表缺失
**错误信息**:
```
Table 'seata_order.undo_log' doesn't exist
```

**解决方案**:
```sql
-- 在每个业务数据库中创建undo_log表
CREATE TABLE undo_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    UNIQUE KEY ux_undo_log (xid, branch_id)
);
```

### 3. Seata连接问题

#### 问题：无法连接到Seata服务器
**错误信息**:
```
can not connect to services-server
```

**解决方案**:
```bash
# 1. 检查Seata服务器是否运行
curl http://**************:7091/api/v1/console/globalSession/query

# 2. 检查网络连接
telnet ************** 8091

# 3. 检查Seata配置
# application.yml中的Seata配置是否正确
seata:
  service:
    grouplist:
      default: **************:8091  # 确保地址正确
```

#### 问题：Seata事务组配置错误
**错误信息**:
```
can not get cluster name in registry config
```

**解决方案**:
```yaml
# 检查事务组配置
seata:
  tx-service-group: my_test_tx_group
  service:
    vgroup-mapping:
      my_test_tx_group: default  # 确保映射正确
```

#### 问题：Seata控制台500错误
**错误信息**:
```
Unknown column 'status' in 'field list'
```

**解决方案**:
```sql
-- 在seata数据库中添加缺失的字段
USE seata;
ALTER TABLE lock_table ADD COLUMN status TINYINT DEFAULT 0 COMMENT '锁状态：0-正常，1-已释放';
```

### 4. 分布式事务问题

#### 问题：事务回滚失败
**错误信息**:
```
Global transaction rollback failed
```

**解决方案**:
```bash
# 1. 检查undo_log表是否存在
# 2. 检查数据源代理配置
# 3. 查看Seata服务器日志
# 4. 确保@GlobalTransactional注解正确使用
```

#### 问题：分支事务注册失败
**错误信息**:
```
Failed to register branch transaction
```

**解决方案**:
```yaml
# 检查数据源代理配置
seata:
  enable-auto-data-source-proxy: true
  data-source-proxy-mode: AT
```

#### 问题：事务超时
**错误信息**:
```
Transaction timeout
```

**解决方案**:
```java
// 增加事务超时时间
@GlobalTransactional(timeoutMills = 60000)  // 60秒
public void createOrder() {
    // 业务逻辑
}
```

### 5. 前端页面问题

#### 问题：跨域请求被阻止
**错误信息**:
```
CORS policy: No 'Access-Control-Allow-Origin' header
```

**解决方案**:
```bash
# 方案1：使用HTTP服务器启动前端
cd web
python -m http.server 8080

# 方案2：确保后端已配置CORS
# CorsConfig.java已添加到各个服务中

# 方案3：使用Chrome禁用安全策略（仅开发环境）
chrome --disable-web-security --user-data-dir="/tmp/chrome_dev"
```

#### 问题：前端无法连接后端服务
**错误信息**:
```
Failed to fetch
```

**解决方案**:
```bash
# 1. 检查后端服务是否运行
curl http://localhost:8081/order/health

# 2. 检查防火墙设置
# 3. 检查前端请求URL是否正确
# 4. 查看浏览器开发者工具的Network面板
```

### 6. 性能问题

#### 问题：服务响应慢
**解决方案**:
```yaml
# 1. 优化数据库连接池
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

# 2. 优化JVM参数
java -Xms512m -Xmx1024m -XX:+UseG1GC -jar app.jar

# 3. 检查数据库索引
# 4. 监控系统资源使用情况
```

#### 问题：内存溢出
**错误信息**:
```
OutOfMemoryError: Java heap space
```

**解决方案**:
```bash
# 增加JVM堆内存
java -Xms1024m -Xmx2048m -jar app.jar

# 分析内存使用情况
jmap -histo <PID>
```

### 7. 日志分析

#### 查看应用日志
```bash
# 查看订单服务日志
tail -f order-service/logs/application.log

# 查看库存服务日志
tail -f stock-service/logs/application.log

# 查看账户服务日志
tail -f account-service/logs/application.log
```

#### 查看Seata日志
```bash
# 查看Seata服务器日志
tail -f seata-server/logs/seata_gc.log
tail -f seata-server/logs/seata-server.log
```

#### 开启调试日志
```yaml
logging:
  level:
    com.example: DEBUG
    io.seata: DEBUG
    org.springframework.cloud.openfeign: DEBUG
```

### 8. 监控和诊断

#### 健康检查
```bash
# 检查所有服务健康状态
curl http://localhost:8081/order/health
curl http://localhost:8082/stock/health
curl http://localhost:8083/account/health
```

#### 查看JVM状态
```bash
# 查看Java进程
jps -l

# 查看JVM内存使用
jstat -gc <PID>

# 查看线程状态
jstack <PID>
```

#### 数据库监控
```sql
-- 查看数据库连接数
SHOW PROCESSLIST;

-- 查看表锁状态
SHOW ENGINE INNODB STATUS;

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

## 🔧 调试技巧

### 1. 分步调试
1. 先测试单个服务的健康检查
2. 再测试服务间的调用
3. 最后测试完整的分布式事务

### 2. 日志分析
1. 开启DEBUG级别日志
2. 关注Seata的事务日志
3. 查看数据库操作日志

### 3. 网络诊断
```bash
# 检查端口连通性
telnet localhost 8081
telnet ************** 8091
telnet ************ 32775

# 检查DNS解析
nslookup **************
nslookup ************
```

### 4. 数据库诊断
```sql
-- 检查事务状态
SELECT * FROM information_schema.innodb_trx;

-- 检查锁等待
SELECT * FROM information_schema.innodb_lock_waits;

-- 检查Seata事务记录
SELECT * FROM seata.global_table ORDER BY gmt_create DESC LIMIT 10;
SELECT * FROM seata.branch_table ORDER BY gmt_create DESC LIMIT 10;
```

## 📞 获取帮助

### 1. 查看文档
- [项目说明.md](./项目说明.md)
- [部署指南.md](./部署指南.md)
- [API文档.md](./API文档.md)

### 2. 在线资源
- [Seata官方文档](https://seata.io/zh-cn/)
- [Spring Cloud Alibaba文档](https://spring-cloud-alibaba-group.github.io/)
- [Spring Boot文档](https://spring.io/projects/spring-boot)

### 3. 社区支持
- [Seata GitHub Issues](https://github.com/seata/seata/issues)
- [Spring Cloud Alibaba GitHub](https://github.com/alibaba/spring-cloud-alibaba)

记住：遇到问题时，首先查看日志，然后检查配置，最后考虑网络和环境因素。大多数问题都可以通过仔细分析错误信息和日志来解决。
