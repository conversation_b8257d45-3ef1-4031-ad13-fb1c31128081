@echo off
echo ================================
echo Spring Cloud Seata Demo 启动脚本
echo ================================

echo 正在启动各个微服务...

echo.
echo 1. 启动订单服务 (端口: 8081)
start "Order Service" cmd /k "cd order-service && mvn spring-boot:run"

echo.
echo 等待5秒...
timeout /t 5 /nobreak > nul

echo 2. 启动库存服务 (端口: 8082)
start "Stock Service" cmd /k "cd stock-service && mvn spring-boot:run"

echo.
echo 等待5秒...
timeout /t 5 /nobreak > nul

echo 3. 启动账户服务 (端口: 8083)
start "Account Service" cmd /k "cd account-service && mvn spring-boot:run"

echo.
echo ================================
echo 所有服务启动完成！
echo ================================
echo 订单服务: http://localhost:8081
echo 库存服务: http://localhost:8082
echo 账户服务: http://localhost:8083
echo Seata控制台: http://**************:7091
echo ================================

pause
