package com.example.order.controller;

import com.example.order.entity.Order;
import com.example.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * 创建订单
     */
    @PostMapping("/create")
    public Map<String, Object> createOrder(@RequestParam Long userId,
                                          @RequestParam Long productId,
                                          @RequestParam Integer count,
                                          @RequestParam BigDecimal price) {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("接收到创建订单请求：userId={}, productId={}, count={}, price={}", 
                    userId, productId, count, price);
            
            Order order = new Order();
            order.setUserId(userId);
            order.setProductId(productId);
            order.setCount(count);
            order.setPrice(price);
            
            Long orderId = orderService.createOrder(order);
            
            result.put("success", true);
            result.put("message", "订单创建成功");
            result.put("orderId", orderId);
            
            log.info("订单创建成功，订单ID：{}", orderId);
        } catch (Exception e) {
            log.error("订单创建失败：", e);
            result.put("success", false);
            result.put("message", "订单创建失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询所有订单
     */
    @GetMapping("/list")
    public Map<String, Object> list() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Order> orders = orderService.getAllOrders();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", orders);
        } catch (Exception e) {
            log.error("查询订单列表失败：", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 重置订单数据
     */
    @PostMapping("/reset")
    public Map<String, Object> reset() {
        Map<String, Object> result = new HashMap<>();
        try {
            orderService.resetData();
            result.put("success", true);
            result.put("message", "订单数据重置成功");
            log.info("订单数据重置成功");
        } catch (Exception e) {
            log.error("重置订单数据失败：", e);
            result.put("success", false);
            result.put("message", "重置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "order-service");
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
