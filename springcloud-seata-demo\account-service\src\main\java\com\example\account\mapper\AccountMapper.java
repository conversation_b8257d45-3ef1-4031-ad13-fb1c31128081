package com.example.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.account.entity.Account;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * 账户Mapper接口
 */
@Mapper
public interface AccountMapper extends BaseMapper<Account> {
    
    /**
     * 扣减账户余额
     * @param userId 用户ID
     * @param amount 扣减金额
     * @return 影响行数
     */
    @Update("UPDATE t_account SET used = used + #{amount}, residue = residue - #{amount}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND residue >= #{amount}")
    int deductBalance(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
}
