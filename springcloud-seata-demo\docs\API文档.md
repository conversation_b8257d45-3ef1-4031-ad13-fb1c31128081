# API接口文档

## 📋 接口概览

本项目提供了完整的RESTful API接口，支持订单创建、库存管理、账户管理等功能。所有接口都支持CORS跨域访问。

## 🔗 基础信息

### 服务地址
- **订单服务**: http://localhost:8081
- **库存服务**: http://localhost:8082
- **账户服务**: http://localhost:8083

### 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": *************
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": *************
}
```

## 📦 订单服务 API

### 1. 健康检查
**接口地址**: `GET /order/health`

**请求示例**:
```bash
curl http://localhost:8081/order/health
```

**响应示例**:
```json
{
  "service": "order-service",
  "status": "UP",
  "timestamp": *************,
  "version": "1.0.0"
}
```

### 2. 创建订单
**接口地址**: `POST /order/create`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| productId | Long | 是 | 商品ID |
| count | Integer | 是 | 购买数量 |
| price | BigDecimal | 是 | 商品单价 |

**请求示例**:
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=1&productId=1&count=2&price=8999.00"
```

**成功响应**:
```json
{
  "success": true,
  "message": "订单创建成功",
  "data": {
    "orderId": 1,
    "userId": 1,
    "productId": 1,
    "count": 2,
    "money": 17998.00,
    "status": 1,
    "createTime": "2025-07-29T12:00:00"
  },
  "timestamp": *************
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "订单创建失败：库存不足",
  "error": "Stock deduction failed: Insufficient stock",
  "timestamp": *************
}
```

### 3. 查询订单
**接口地址**: `GET /order/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 订单ID |

**请求示例**:
```bash
curl http://localhost:8081/order/1
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "id": 1,
    "userId": 1,
    "productId": 1,
    "count": 2,
    "money": 17998.00,
    "status": 1,
    "createTime": "2025-07-29T12:00:00"
  },
  "timestamp": *************
}
```

### 4. 查询用户订单列表
**接口地址**: `GET /order/user/{userId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**查询参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |

**请求示例**:
```bash
curl "http://localhost:8081/order/user/1?page=1&size=10"
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "total": 5,
    "page": 1,
    "size": 10,
    "orders": [
      {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "count": 2,
        "money": 17998.00,
        "status": 1,
        "createTime": "2025-07-29T12:00:00"
      }
    ]
  },
  "timestamp": *************
}
```

## 📦 库存服务 API

### 1. 健康检查
**接口地址**: `GET /stock/health`

**请求示例**:
```bash
curl http://localhost:8082/stock/health
```

**响应示例**:
```json
{
  "service": "stock-service",
  "status": "UP",
  "timestamp": *************,
  "version": "1.0.0"
}
```

### 2. 扣减库存
**接口地址**: `POST /stock/decrease`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |
| count | Integer | 是 | 扣减数量 |

**请求示例**:
```bash
curl -X POST "http://localhost:8082/stock/decrease" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "productId=1&count=2"
```

**成功响应**:
```json
{
  "success": true,
  "message": "库存扣减成功",
  "data": {
    "productId": 1,
    "productName": "iPhone 15 Pro",
    "beforeStock": 100,
    "afterStock": 98,
    "decreaseCount": 2
  },
  "timestamp": *************
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "库存扣减失败：库存不足",
  "error": "Insufficient stock: available=50, required=100",
  "timestamp": *************
}
```

### 3. 查询库存
**接口地址**: `GET /stock/{productId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |

**请求示例**:
```bash
curl http://localhost:8082/stock/1
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "id": 1,
    "productId": 1,
    "productName": "iPhone 15 Pro",
    "total": 100,
    "used": 2,
    "residue": 98
  },
  "timestamp": *************
}
```

### 4. 查询所有库存
**接口地址**: `GET /stock/list`

**请求示例**:
```bash
curl http://localhost:8082/stock/list
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "productId": 1,
      "productName": "iPhone 15 Pro",
      "total": 100,
      "used": 2,
      "residue": 98
    },
    {
      "id": 2,
      "productId": 2,
      "productName": "MacBook Pro",
      "total": 50,
      "used": 0,
      "residue": 50
    }
  ],
  "timestamp": *************
}
```

## 💰 账户服务 API

### 1. 健康检查
**接口地址**: `GET /account/health`

**请求示例**:
```bash
curl http://localhost:8083/account/health
```

**响应示例**:
```json
{
  "service": "account-service",
  "status": "UP",
  "timestamp": *************,
  "version": "1.0.0"
}
```

### 2. 扣减余额
**接口地址**: `POST /account/decrease`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |
| money | BigDecimal | 是 | 扣减金额 |

**请求示例**:
```bash
curl -X POST "http://localhost:8083/account/decrease" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=1&money=17998.00"
```

**成功响应**:
```json
{
  "success": true,
  "message": "余额扣减成功",
  "data": {
    "userId": 1,
    "userName": "张三",
    "beforeBalance": 100000.00,
    "afterBalance": 82002.00,
    "decreaseMoney": 17998.00
  },
  "timestamp": *************
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "余额扣减失败：余额不足",
  "error": "Insufficient balance: available=50000.00, required=79995.00",
  "timestamp": *************
}
```

### 3. 查询账户
**接口地址**: `GET /account/{userId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | Long | 是 | 用户ID |

**请求示例**:
```bash
curl http://localhost:8083/account/1
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "id": 1,
    "userId": 1,
    "userName": "张三",
    "total": 100000.00,
    "used": 17998.00,
    "residue": 82002.00
  },
  "timestamp": *************
}
```

### 4. 查询所有账户
**接口地址**: `GET /account/list`

**请求示例**:
```bash
curl http://localhost:8083/account/list
```

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "userId": 1,
      "userName": "张三",
      "total": 100000.00,
      "used": 17998.00,
      "residue": 82002.00
    },
    {
      "id": 2,
      "userId": 2,
      "userName": "李四",
      "total": 50000.00,
      "used": 0.00,
      "residue": 50000.00
    }
  ],
  "timestamp": *************
}
```

## 🔧 状态码说明

### HTTP状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务状态码
| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 数据不存在 |
| 1003 | 库存不足 |
| 1004 | 余额不足 |
| 1005 | 分布式事务失败 |

## 🧪 测试用例

### 正常订单创建
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=1&productId=1&count=1&price=8999.00"
```

### 库存不足测试
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=1&productId=1&count=200&price=8999.00"
```

### 余额不足测试
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=2&productId=2&count=5&price=15999.00"
```

## 📝 注意事项

### 1. 请求头设置
- Content-Type: application/x-www-form-urlencoded
- Accept: application/json

### 2. 参数验证
- 所有必填参数不能为空
- 数值类型参数必须为正数
- 金额参数保留两位小数

### 3. 事务处理
- 订单创建是分布式事务操作
- 任何步骤失败都会触发全局回滚
- 事务超时时间为30秒

### 4. 错误处理
- 所有异常都会返回统一格式的错误响应
- 分布式事务异常会包含详细的错误信息
- 建议客户端根据success字段判断操作结果
