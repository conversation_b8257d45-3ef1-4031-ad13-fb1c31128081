<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring Cloud Seata 分布式事务演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }

        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        .status-loading { background-color: #ff9800; }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: #333;
        }

        .result-area {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .data-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }

        .data-table tr:hover {
            background: #f5f5f5;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: bold;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .scenario-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }

        .links {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .links h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .links a {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
            color: #1976d2;
            text-decoration: none;
            font-weight: bold;
        }

        .links a:hover {
            text-decoration: underline;
        }

        .status-success {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-warning {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-error {
            background: #f44336;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Spring Cloud Seata 分布式事务演示</h1>
            <p>实时验证分布式事务的一致性和回滚机制</p>
        </div>

        <div class="main-content">
            <!-- 服务状态监控 -->
            <div class="section">
                <h2>📊 服务状态监控</h2>
                <div class="status-grid">
                    <div class="status-card">
                        <h3><span id="order-status" class="status-indicator status-loading"></span>订单服务</h3>
                        <p>端口: 8081</p>
                        <p id="order-info">检查中...</p>
                    </div>
                    <div class="status-card">
                        <h3><span id="stock-status" class="status-indicator status-loading"></span>库存服务</h3>
                        <p>端口: 8082</p>
                        <p id="stock-info">检查中...</p>
                    </div>
                    <div class="status-card">
                        <h3><span id="account-status" class="status-indicator status-loading"></span>账户服务</h3>
                        <p>端口: 8083</p>
                        <p id="account-info">检查中...</p>
                    </div>
                </div>
                <button class="btn btn-secondary" onclick="checkAllServices()">🔄 刷新服务状态</button>
            </div>

            <!-- 数据查看 -->
            <div class="section">
                <h2>📋 当前数据状态</h2>
                <button class="btn btn-secondary" onclick="loadAllData()">📊 刷新数据</button>
                <button class="btn btn-secondary" onclick="resetData()">🔄 重置测试数据</button>
                <button class="btn btn-secondary" onclick="toggleTestMode()" id="testModeBtn">🧪 启用测试模式</button>
                
                <div id="data-display">
                    <p>点击"刷新数据"查看当前数据状态</p>
                </div>
            </div>

            <!-- 分布式事务测试 -->
            <div class="section">
                <h2>🧪 分布式事务测试</h2>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div>
                        <h3>自定义订单</h3>
                        <div class="form-group">
                            <label for="userId">用户ID:</label>
                            <select id="userId">
                                <option value="1">1 - 张三 (余额: ¥100,000)</option>
                                <option value="2">2 - 李四 (余额: ¥50,000)</option>
                                <option value="3">3 - 王五 (余额: ¥30,000)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="productId">商品:</label>
                            <select id="productId">
                                <option value="1">iPhone 15 Pro (¥8,999)</option>
                                <option value="2">MacBook Pro (¥15,999)</option>
                                <option value="3">iPad Air (¥4,599)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="count">数量:</label>
                            <input type="number" id="count" value="1" min="1" max="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="price">单价:</label>
                            <input type="number" id="price" value="8999" step="0.01" min="0">
                        </div>
                        
                        <button class="btn" onclick="createOrder()">🛒 创建订单</button>
                    </div>
                    
                    <div>
                        <h3>快速测试场景</h3>
                        <div class="scenario-buttons">
                            <button class="btn" onclick="testNormalOrder()">✅ 正常订单</button>
                            <button class="btn btn-danger" onclick="testInsufficientStock()">❌ 库存不足</button>
                            <button class="btn btn-danger" onclick="testInsufficientBalance()">💰 余额不足</button>
                            <button class="btn btn-secondary" onclick="testConcurrentOrders()">⚡ 并发测试</button>
                        </div>
                        
                        <div class="alert alert-info">
                            <strong>测试说明:</strong><br>
                            • 正常订单: 验证事务成功提交<br>
                            • 库存不足: 验证事务自动回滚<br>
                            • 余额不足: 验证事务自动回滚<br>
                            • 并发测试: 验证事务隔离性
                        </div>
                    </div>
                </div>
                
                <div class="result-area" id="result-area">
                    等待操作结果...
                </div>
            </div>

            <!-- 外部链接 -->
            <div class="links">
                <h3>🔗 相关链接</h3>
                <a href="http://**************:7091" target="_blank">Seata 控制台</a>
                <a href="http://localhost:8081/order/health" target="_blank">订单服务健康检查</a>
                <a href="http://localhost:8082/stock/health" target="_blank">库存服务健康检查</a>
                <a href="http://localhost:8083/account/health" target="_blank">账户服务健康检查</a>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let orderCounter = 1;
        let testMode = false; // 测试模式开关
        
        // 页面加载时初始化
        window.onload = function() {
            checkAllServices();
            updatePriceByProduct();
        };
        
        // 商品价格映射
        const productPrices = {
            '1': 8999,
            '2': 15999,
            '3': 4599
        };
        
        // 根据商品更新价格
        document.getElementById('productId').addEventListener('change', updatePriceByProduct);
        
        function updatePriceByProduct() {
            const productId = document.getElementById('productId').value;
            const priceInput = document.getElementById('price');
            priceInput.value = productPrices[productId] || 8999;
        }
        
        // 检查所有服务状态
        async function checkAllServices() {
            const services = [
                { name: 'order', port: 8081, statusId: 'order-status', infoId: 'order-info' },
                { name: 'stock', port: 8082, statusId: 'stock-status', infoId: 'stock-info' },
                { name: 'account', port: 8083, statusId: 'account-status', infoId: 'account-info' }
            ];
            
            for (const service of services) {
                await checkServiceStatus(service);
            }
        }
        
        // 检查单个服务状态
        async function checkServiceStatus(service) {
            const statusElement = document.getElementById(service.statusId);
            const infoElement = document.getElementById(service.infoId);
            
            statusElement.className = 'status-indicator status-loading';
            infoElement.textContent = '检查中...';
            
            try {
                const response = await fetch(`http://localhost:${service.port}/${service.name}/health`);
                if (response.ok) {
                    const data = await response.json();
                    statusElement.className = 'status-indicator status-online';
                    infoElement.textContent = `在线 - ${data.timestamp ? new Date(data.timestamp).toLocaleTimeString() : '正常'}`;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusElement.className = 'status-indicator status-offline';
                infoElement.textContent = `离线 - ${error.message}`;
            }
        }

        // 切换测试模式
        function toggleTestMode() {
            testMode = !testMode;
            const btn = document.getElementById('testModeBtn');
            if (testMode) {
                btn.textContent = '🔧 切换到实际模式';
                btn.className = 'btn btn-danger';
            } else {
                btn.textContent = '🧪 启用测试模式';
                btn.className = 'btn btn-secondary';
            }
            // 重新加载数据
            loadAllData();
        }

        // 模拟数据
        const mockData = {
            stocks: [
                { productId: 1, productName: "iPhone 15 Pro", total: 100, used: 5, residue: 95 },
                { productId: 2, productName: "MacBook Pro", total: 50, used: 2, residue: 48 },
                { productId: 3, productName: "iPad Air", total: 80, used: 0, residue: 80 }
            ],
            accounts: [
                { userId: 1, userName: "张三", total: 100000.00, used: 44995.00, residue: 55005.00 },
                { userId: 2, userName: "李四", total: 50000.00, used: 31998.00, residue: 18002.00 },
                { userId: 3, userName: "王五", total: 30000.00, used: 0.00, residue: 30000.00 }
            ],
            orders: [
                { id: 1, userId: 1, productId: 1, count: 2, price: 8999.00, totalAmount: 17998.00, status: 1, createTime: "2025-07-29T10:30:00" },
                { id: 2, userId: 1, productId: 1, count: 3, price: 8999.00, totalAmount: 26997.00, status: 1, createTime: "2025-07-29T11:15:00" },
                { id: 3, userId: 2, productId: 2, count: 2, price: 15999.00, totalAmount: 31998.00, status: 1, createTime: "2025-07-29T12:00:00" }
            ]
        };

        // 加载所有数据
        async function loadAllData() {
            const dataDisplay = document.getElementById('data-display');
            dataDisplay.innerHTML = '<div class="loading"></div> 加载数据中...';

            try {
                let stockData, accountData, orderData;

                if (testMode) {
                    // 测试模式：使用模拟数据
                    stockData = { success: true, message: "查询成功", data: mockData.stocks };
                    accountData = { success: true, message: "查询成功", data: mockData.accounts };
                    orderData = { success: true, message: "查询成功", data: mockData.orders };
                } else {
                    // 实际模式：调用真实API
                    const [stockResponse, accountResponse, orderResponse] = await Promise.all([
                        fetch('http://localhost:8082/stock/list').catch(() => ({ ok: false, json: () => ({ success: false, message: '库存服务不可用' }) })),
                        fetch('http://localhost:8083/account/list').catch(() => ({ ok: false, json: () => ({ success: false, message: '账户服务不可用' }) })),
                        fetch('http://localhost:8081/order/list').catch(() => ({ ok: false, json: () => ({ success: false, message: '订单服务不可用' }) }))
                    ]);

                    stockData = stockResponse.ok ? await stockResponse.json() : { success: false, message: '库存服务连接失败' };
                    accountData = accountResponse.ok ? await accountResponse.json() : { success: false, message: '账户服务连接失败' };
                    orderData = orderResponse.ok ? await orderResponse.json() : { success: false, message: '订单服务连接失败' };
                }

                let html = '';

                // 库存数据表格
                html += '<h3>📦 库存数据</h3>';
                if (stockData.success && stockData.data) {
                    html += `
                    <table class="data-table">
                        <tr><th>商品ID</th><th>商品名称</th><th>总库存</th><th>已用库存</th><th>剩余库存</th></tr>`;
                    stockData.data.forEach(stock => {
                        html += `
                        <tr>
                            <td>${stock.productId}</td>
                            <td>${stock.productName}</td>
                            <td>${stock.total}</td>
                            <td>${stock.used}</td>
                            <td>${stock.residue}</td>
                        </tr>`;
                    });
                    html += '</table>';
                } else {
                    html += `<div class="alert alert-error">库存数据加载失败: ${stockData.message}</div>`;
                }

                // 账户数据表格
                html += '<h3>💰 账户数据</h3>';
                if (accountData.success && accountData.data) {
                    html += `
                    <table class="data-table">
                        <tr><th>用户ID</th><th>用户名</th><th>总余额</th><th>已用余额</th><th>可用余额</th></tr>`;
                    accountData.data.forEach(account => {
                        html += `
                        <tr>
                            <td>${account.userId}</td>
                            <td>${account.userName}</td>
                            <td>${account.total.toFixed(2)}</td>
                            <td>${account.used.toFixed(2)}</td>
                            <td>${account.residue.toFixed(2)}</td>
                        </tr>`;
                    });
                    html += '</table>';
                } else {
                    html += `<div class="alert alert-error">账户数据加载失败: ${accountData.message}</div>`;
                }

                // 订单数据表格
                html += '<h3>📋 订单数据</h3>';
                if (orderData.success && orderData.data && orderData.data.length > 0) {
                    html += `
                    <table class="data-table">
                        <tr><th>订单ID</th><th>用户ID</th><th>商品ID</th><th>数量</th><th>单价</th><th>总金额</th><th>状态</th><th>创建时间</th></tr>`;
                    orderData.data.forEach(order => {
                        const statusText = order.status === 0 ? '创建中' : order.status === 1 ? '已完成' : '未知';
                        const statusClass = order.status === 1 ? 'success' : 'warning';
                        html += `
                        <tr>
                            <td>${order.id}</td>
                            <td>${order.userId}</td>
                            <td>${order.productId}</td>
                            <td>${order.count}</td>
                            <td>¥${order.price ? order.price.toFixed(2) : '0.00'}</td>
                            <td>¥${order.totalAmount ? order.totalAmount.toFixed(2) : '0.00'}</td>
                            <td><span class="status-${statusClass}">${statusText}</span></td>
                            <td>${order.createTime ? new Date(order.createTime).toLocaleString() : '-'}</td>
                        </tr>`;
                    });
                    html += '</table>';
                } else if (orderData.success) {
                    html += '<div style="text-align: center; color: #666; padding: 20px;">暂无订单数据</div>';
                } else {
                    html += `<div class="alert alert-error">订单数据加载失败: ${orderData.message}</div>`;
                }

                dataDisplay.innerHTML = html;
            } catch (error) {
                dataDisplay.innerHTML = `<div class="alert alert-error">加载数据失败: ${error.message}</div>`;
            }
        }

        // 重置测试数据
        async function resetData() {
            if (!confirm('确定要重置所有测试数据吗？这将清空所有订单并恢复初始库存和余额。')) {
                return;
            }

            const resultArea = document.getElementById('result-area');
            resultArea.innerHTML = '<div class="loading"></div> 正在重置数据...';

            try {
                let orderResult, stockResult, accountResult;

                if (testMode) {
                    // 测试模式：模拟重置成功
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟延迟
                    orderResult = { success: true, message: '订单数据重置成功' };
                    stockResult = { success: true, message: '库存数据重置成功' };
                    accountResult = { success: true, message: '账户数据重置成功' };

                    // 重置模拟数据
                    mockData.orders = [];
                    mockData.stocks.forEach(stock => {
                        stock.used = 0;
                        stock.residue = stock.total;
                    });
                    mockData.accounts.forEach(account => {
                        account.used = 0;
                        account.residue = account.total;
                    });
                } else {
                    // 实际模式：调用真实API
                    const resetPromises = [
                        fetch('http://localhost:8081/order/reset', { method: 'POST' })
                            .then(res => res.json())
                            .catch(() => ({ success: false, message: '订单服务重置失败' })),
                        fetch('http://localhost:8082/stock/reset', { method: 'POST' })
                            .then(res => res.json())
                            .catch(() => ({ success: false, message: '库存服务重置失败' })),
                        fetch('http://localhost:8083/account/reset', { method: 'POST' })
                            .then(res => res.json())
                            .catch(() => ({ success: false, message: '账户服务重置失败' }))
                    ];

                    [orderResult, stockResult, accountResult] = await Promise.all(resetPromises);
                }

                let successCount = 0;
                let errorMessages = [];

                if (orderResult.success) {
                    successCount++;
                } else {
                    errorMessages.push(`订单重置失败: ${orderResult.message}`);
                }

                if (stockResult.success) {
                    successCount++;
                } else {
                    errorMessages.push(`库存重置失败: ${stockResult.message}`);
                }

                if (accountResult.success) {
                    successCount++;
                } else {
                    errorMessages.push(`账户重置失败: ${accountResult.message}`);
                }

                if (successCount === 3) {
                    resultArea.innerHTML = `
                    <div class="alert alert-success">
                    ✅ 数据重置成功！

                    重置内容：
                    • ✅ 清空所有订单数据
                    • ✅ 恢复商品库存到初始状态
                    • ✅ 恢复用户余额到初始状态

                    时间: ${new Date().toLocaleString()}

                    注意：Seata事务记录需要手动清理
                    </div>`;
                } else {
                    resultArea.innerHTML = `
                    <div class="alert alert-error">
                    ⚠️ 数据重置部分失败！

                    成功重置: ${successCount}/3 个服务

                    失败详情：
                    ${errorMessages.map(msg => `• ${msg}`).join('\n')}

                    时间: ${new Date().toLocaleString()}
                    </div>`;
                }

                // 刷新数据显示
                setTimeout(loadAllData, 1000);
            } catch (error) {
                resultArea.innerHTML = `<div class="alert alert-error">重置数据失败: ${error.message}</div>`;
            }
        }

        // 创建订单
        async function createOrder() {
            const userId = document.getElementById('userId').value;
            const productId = document.getElementById('productId').value;
            const count = document.getElementById('count').value;
            const price = document.getElementById('price').value;

            const resultArea = document.getElementById('result-area');
            resultArea.innerHTML = '<div class="loading"></div> 创建订单中...';

            const orderData = {
                userId: userId,
                productId: productId,
                count: count,
                price: price
            };

            try {
                const response = await fetch('http://localhost:8081/order/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(orderData)
                });

                const result = await response.json();

                if (result.success) {
                    resultArea.innerHTML = `
                    <div class="alert alert-success">
                    ✅ 订单创建成功！

                    订单信息：
                    • 订单ID: ${result.orderId || orderCounter++}
                    • 用户ID: ${userId}
                    • 商品ID: ${productId}
                    • 数量: ${count}
                    • 单价: ¥${price}
                    • 总金额: ¥${(count * price).toFixed(2)}
                    • 创建时间: ${new Date().toLocaleString()}

                    事务状态: 已提交 ✅
                    </div>`;
                } else {
                    resultArea.innerHTML = `
                    <div class="alert alert-error">
                    ❌ 订单创建失败！

                    失败原因: ${result.message || '未知错误'}

                    订单信息：
                    • 用户ID: ${userId}
                    • 商品ID: ${productId}
                    • 数量: ${count}
                    • 单价: ¥${price}
                    • 总金额: ¥${(count * price).toFixed(2)}
                    • 尝试时间: ${new Date().toLocaleString()}

                    事务状态: 已回滚 🔄
                    </div>`;
                }

                // 刷新数据显示
                setTimeout(loadAllData, 1000);

            } catch (error) {
                resultArea.innerHTML = `
                <div class="alert alert-error">
                ❌ 网络请求失败！

                错误信息: ${error.message}

                请检查：
                • 订单服务是否正常运行 (端口8081)
                • 网络连接是否正常
                • 服务器是否启动
                </div>`;
            }
        }

        // 测试正常订单
        async function testNormalOrder() {
            document.getElementById('userId').value = '1';
            document.getElementById('productId').value = '1';
            document.getElementById('count').value = '1';
            document.getElementById('price').value = '8999';

            await createOrder();
        }

        // 测试库存不足
        async function testInsufficientStock() {
            document.getElementById('userId').value = '1';
            document.getElementById('productId').value = '1';
            document.getElementById('count').value = '200';
            document.getElementById('price').value = '8999';

            await createOrder();
        }

        // 测试余额不足
        async function testInsufficientBalance() {
            document.getElementById('userId').value = '2';
            document.getElementById('productId').value = '2';
            document.getElementById('count').value = '5';
            document.getElementById('price').value = '15999';

            await createOrder();
        }

        // 并发测试
        async function testConcurrentOrders() {
            const resultArea = document.getElementById('result-area');
            resultArea.innerHTML = '<div class="loading"></div> 执行并发测试...';

            const orders = [
                { userId: '1', productId: '1', count: '2', price: '8999' },
                { userId: '2', productId: '1', count: '3', price: '8999' },
                { userId: '3', productId: '1', count: '1', price: '8999' }
            ];

            try {
                const promises = orders.map(order =>
                    fetch('http://localhost:8081/order/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams(order)
                    }).then(res => res.json())
                );

                const results = await Promise.all(promises);

                let resultText = `
                <div class="alert alert-info">
                🔄 并发测试完成！

                测试场景: 3个用户同时购买同一商品
                执行时间: ${new Date().toLocaleString()}

                结果详情:
                `;

                results.forEach((result, index) => {
                    const order = orders[index];
                    resultText += `
                • 订单${index + 1} (用户${order.userId}): ${result.success ? '✅ 成功' : '❌ 失败 - ' + result.message}`;
                });

                resultText += `

                说明: 并发测试验证了分布式事务的隔离性和一致性
                </div>`;

                resultArea.innerHTML = resultText;

                // 刷新数据显示
                setTimeout(loadAllData, 1000);

            } catch (error) {
                resultArea.innerHTML = `<div class="alert alert-error">并发测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
