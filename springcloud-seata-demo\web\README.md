# Spring Cloud Seata 分布式事务演示前端

## 🚀 快速启动

### 方式一：直接打开HTML文件
```bash
# 直接用浏览器打开
open springcloud-seata-demo/web/index.html
# 或者双击 index.html 文件
```

### 方式二：使用HTTP服务器（推荐）
```bash
# 使用Python启动HTTP服务器
cd springcloud-seata-demo/web
python -m http.server 8080

# 或者使用Node.js
npx http-server -p 8080

# 然后访问: http://localhost:8080
```

## 📋 功能特性

### 🔍 服务状态监控
- 实时检查订单服务、库存服务、账户服务的运行状态
- 显示服务在线/离线状态和响应时间
- 一键刷新所有服务状态

### 📊 数据状态查看
- 查看当前库存数据（商品库存情况）
- 查看账户数据（用户余额情况）
- 查看订单数据（历史订单记录）
- 支持数据重置功能

### 🧪 分布式事务测试
- **自定义订单创建**：手动选择用户、商品、数量和价格
- **快速测试场景**：
  - ✅ 正常订单：验证事务成功提交
  - ❌ 库存不足：验证事务自动回滚
  - 💰 余额不足：验证事务自动回滚
  - ⚡ 并发测试：验证事务隔离性

### 🔗 外部链接
- Seata控制台：监控全局事务状态
- 各服务健康检查：快速验证服务状态

## 🎯 使用说明

### 1. 确保服务运行
在使用前端页面之前，请确保以下服务正常运行：
- 订单服务：http://localhost:8081
- 库存服务：http://localhost:8082
- 账户服务：http://localhost:8083
- Seata服务器：http://**************:7091

### 2. 测试步骤
1. **检查服务状态**：点击"刷新服务状态"确认所有服务在线
2. **查看初始数据**：点击"刷新数据"查看当前数据状态
3. **执行测试**：
   - 使用快速测试按钮验证各种场景
   - 或者自定义订单参数进行测试
4. **观察结果**：查看操作结果和事务状态
5. **监控事务**：访问Seata控制台查看事务详情

### 3. 测试场景说明

#### 正常订单测试
- 用户：张三（余额充足）
- 商品：iPhone 15 Pro（库存充足）
- 数量：1个
- 预期：订单创建成功，库存减少，余额扣减

#### 库存不足测试
- 用户：张三
- 商品：iPhone 15 Pro
- 数量：200个（超出库存）
- 预期：订单创建失败，事务回滚，数据不变

#### 余额不足测试
- 用户：李四（余额5万）
- 商品：MacBook Pro（单价1.6万）
- 数量：5个（总价8万，超出余额）
- 预期：订单创建失败，事务回滚，数据不变

#### 并发测试
- 3个用户同时购买同一商品
- 验证事务的隔离性和一致性
- 观察哪些订单成功，哪些失败

## 🔧 技术特点

### 前端技术
- 纯HTML + CSS + JavaScript
- 响应式设计，支持移动端
- 现代化UI界面
- 实时状态更新

### 后端集成
- RESTful API调用
- 跨域请求支持
- 错误处理和用户友好提示
- 异步操作和加载状态

### 分布式事务验证
- 实时监控事务状态
- 可视化事务结果
- 支持多种测试场景
- 详细的错误信息展示

## 🚨 注意事项

1. **跨域问题**：如果遇到跨域错误，请使用HTTP服务器方式启动
2. **服务依赖**：确保所有微服务和Seata服务器正常运行
3. **网络连接**：确保能够访问所有服务端口
4. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari等）

## 📱 界面预览

- 🎨 现代化渐变设计
- 📊 实时状态指示器
- 🔄 动态加载动画
- 📋 数据表格展示
- 🎯 一键测试按钮
- 📝 详细结果展示

## 🔗 相关链接

- [Seata官方文档](https://seata.io/zh-cn/)
- [Spring Cloud Alibaba](https://spring-cloud-alibaba-group.github.io/github-pages/hoxton/zh-cn/index.html)
- [分布式事务原理](https://seata.io/zh-cn/docs/overview/what-is-seata.html)
