# 部署指南

## 🚀 环境要求

### 必需环境
- **JDK**: 8+ (推荐JDK 11)
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Git**: 用于克隆项目

### 可选环境
- **Python**: 3.x (用于启动Web界面)
- **Node.js**: 14+ (用于启动Web界面)
- **Docker**: 用于容器化部署

## 📋 部署步骤

### 1. 环境准备

#### 1.1 检查Java环境
```bash
java -version
mvn -version
```

#### 1.2 检查MySQL连接
```bash
mysql -h ************ -P 32775 -u root -p123456 -e "SELECT VERSION();"
```

#### 1.3 检查Seata服务器
```bash
curl http://**************:7091/api/v1/console/globalSession/query
```

### 2. 项目部署

#### 2.1 克隆项目
```bash
git clone <项目地址>
cd springcloud-seata-demo
```

#### 2.2 数据库初始化
```bash
# 执行业务数据库初始化脚本
mysql -h ************ -P 32775 -u root -p123456 < sql/init.sql

# 验证数据库创建
mysql -h ************ -P 32775 -u root -p123456 -e "SHOW DATABASES LIKE 'seata_%';"
```

#### 2.3 编译项目
```bash
# 编译所有模块
mvn clean compile

# 或者分别编译每个服务
cd order-service && mvn clean compile && cd ..
cd stock-service && mvn clean compile && cd ..
cd account-service && mvn clean compile && cd ..
```

#### 2.4 启动服务

**方式一：使用启动脚本**
```bash
# Linux/Mac
chmod +x start-services.sh
./start-services.sh

# Windows
start-services.bat
```

**方式二：手动启动**
```bash
# 启动订单服务
cd order-service
mvn spring-boot:run &

# 启动库存服务
cd ../stock-service
mvn spring-boot:run &

# 启动账户服务
cd ../account-service
mvn spring-boot:run &
```

#### 2.5 启动Web界面
```bash
cd web
chmod +x start-web.sh
./start-web.sh
```

### 3. 验证部署

#### 3.1 检查服务状态
```bash
# 检查订单服务
curl http://localhost:8081/order/health

# 检查库存服务
curl http://localhost:8082/stock/health

# 检查账户服务
curl http://localhost:8083/account/health
```

#### 3.2 访问Web界面
打开浏览器访问：http://localhost:8080

#### 3.3 访问Seata控制台
打开浏览器访问：http://**************:7091
- 用户名：seata
- 密码：seata

## 🐳 Docker部署

### 1. 创建Dockerfile

#### 订单服务Dockerfile
```dockerfile
FROM openjdk:8-jre-slim

WORKDIR /app

COPY target/order-service-1.0.0.jar app.jar

EXPOSE 8081

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 库存服务Dockerfile
```dockerfile
FROM openjdk:8-jre-slim

WORKDIR /app

COPY target/stock-service-1.0.0.jar app.jar

EXPOSE 8082

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 账户服务Dockerfile
```dockerfile
FROM openjdk:8-jre-slim

WORKDIR /app

COPY target/account-service-1.0.0.jar app.jar

EXPOSE 8083

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  order-service:
    build: ./order-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - mysql
    networks:
      - seata-network

  stock-service:
    build: ./stock-service
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - mysql
    networks:
      - seata-network

  account-service:
    build: ./account-service
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    depends_on:
      - mysql
    networks:
      - seata-network

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: seata
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - seata-network

  web:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./web:/usr/share/nginx/html
    networks:
      - seata-network

volumes:
  mysql-data:

networks:
  seata-network:
    driver: bridge
```

### 3. Docker部署命令
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## ⚙️ 配置说明

### 1. 数据库配置

#### 开发环境配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************
    username: root
    password: 123456
```

#### 生产环境配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:seata_order}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
```

### 2. Seata配置

#### 开发环境配置
```yaml
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: my_test_tx_group
  service:
    vgroup-mapping:
      my_test_tx_group: default
    grouplist:
      default: **************:8091
```

#### 生产环境配置
```yaml
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: my_test_tx_group
  service:
    vgroup-mapping:
      my_test_tx_group: default
    grouplist:
      default: ${SEATA_HOST:localhost}:${SEATA_PORT:8091}
```

### 3. 端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| 订单服务 | 8081 | HTTP API端口 |
| 库存服务 | 8082 | HTTP API端口 |
| 账户服务 | 8083 | HTTP API端口 |
| Web界面 | 8080 | 前端页面端口 |
| Seata服务器 | 8091 | Seata通信端口 |
| Seata控制台 | 7091 | Seata管理界面 |
| MySQL数据库 | 32775 | 数据库连接端口 |

## 🔧 性能优化

### 1. JVM参数优化
```bash
# 生产环境JVM参数
java -Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -jar app.jar
```

### 2. 数据库连接池优化
```yaml
spring:
  datasource:
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
```

### 3. Seata性能优化
```yaml
seata:
  client:
    rm:
      async-commit-buffer-limit: 10000
      report-retry-count: 5
      table-meta-check-enable: false
    tm:
      commit-retry-count: 5
      rollback-retry-count: 5
```

## 📊 监控配置

### 1. 应用监控
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

### 2. 日志配置
```yaml
logging:
  level:
    com.example: DEBUG
    io.seata: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
```

## 🚨 安全配置

### 1. 数据库安全
- 使用专用数据库用户
- 限制数据库访问权限
- 启用SSL连接

### 2. 应用安全
- 配置防火墙规则
- 使用HTTPS协议
- 实施访问控制

### 3. Seata安全
- 配置Seata认证
- 限制Seata控制台访问
- 使用安全的通信协议

## 📝 部署检查清单

### 部署前检查
- [ ] Java环境已安装并配置
- [ ] Maven环境已安装并配置
- [ ] MySQL数据库可正常连接
- [ ] Seata服务器正常运行
- [ ] 网络端口已开放

### 部署后检查
- [ ] 所有微服务正常启动
- [ ] 数据库连接正常
- [ ] Seata注册成功
- [ ] Web界面可正常访问
- [ ] API接口响应正常
- [ ] 分布式事务功能正常

### 性能检查
- [ ] 服务响应时间正常
- [ ] 数据库连接池正常
- [ ] 内存使用率正常
- [ ] CPU使用率正常
- [ ] 日志输出正常
