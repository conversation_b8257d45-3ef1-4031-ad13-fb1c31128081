# Spring Cloud Seata 分布式事务Demo

这是一个基于Spring Cloud和Seata实现的分布式事务演示项目，包含订单、库存、账户三个微服务，演示在分布式环境下如何保证数据一致性。

## 🏗️ 项目架构

```
springcloud-seata-demo/
├── order-service/          # 订单服务 (端口: 8081)
├── stock-service/          # 库存服务 (端口: 8082)
├── account-service/        # 账户服务 (端口: 8083)
├── sql/                    # 数据库初始化脚本
├── test-api.http          # API测试文件
├── start-services.bat     # Windows启动脚本
├── start-services.sh      # Linux/Mac启动脚本
└── README.md              # 项目说明文档
```

## 🛠️ 技术栈

- **Spring Boot**: 2.7.18
- **Spring Cloud**: 2021.0.8
- **Spring Cloud Alibaba**: 2021.0.5.0
- **Seata**: 1.5.2
- **MySQL**: 8.0+
- **MyBatis Plus**: 3.5.3.1
- **OpenFeign**: 服务间调用

## 📋 业务流程

### 创建订单流程
1. **订单服务**: 创建订单记录
2. **库存服务**: 扣减商品库存
3. **账户服务**: 扣减用户余额
4. **订单服务**: 更新订单状态为已完成

### 分布式事务保证
- 使用Seata的AT模式实现分布式事务
- 任何一个步骤失败，整个事务自动回滚
- 保证数据的最终一致性

## 🚀 快速开始

### 1. 环境准备

#### 必需环境
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Seata Server 1.5.2

#### Seata服务器
确保Seata服务器已启动并运行在 `121.37.42.37:8091`

### 2. 数据库初始化

执行SQL脚本创建数据库和表：
```bash
mysql -u root -p < sql/init.sql
```

### 3. 启动服务

#### Windows环境
```bash
start-services.bat
```

#### Linux/Mac环境
```bash
chmod +x start-services.sh
./start-services.sh
```

### 4. 🎨 启动Web测试界面

我们提供了一个直观的Web界面来测试分布式事务：

```bash
# 启动Web界面
cd web
chmod +x start-web.sh
./start-web.sh    # Linux/Mac
# 或
start-web.bat     # Windows

# 然后访问: http://localhost:8080
```

**Web界面功能：**
- 📊 实时服务状态监控
- 📋 数据状态查看和重置
- 🧪 一键分布式事务测试
- 🔄 并发测试验证
- 📈 可视化结果展示
- 🔗 快速访问Seata控制台

#### 手动启动
```bash
# 启动订单服务
cd order-service
mvn spring-boot:run

# 启动库存服务
cd stock-service
mvn spring-boot:run

# 启动账户服务
cd account-service
mvn spring-boot:run
```

### 4. 验证服务

访问健康检查接口确认服务启动成功：
- 订单服务: http://localhost:8081/order/health
- 库存服务: http://localhost:8082/stock/health
- 账户服务: http://localhost:8083/account/health

## 🧪 测试用例

### 1. 正常订单创建（事务成功）
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=1&productId=1&count=2&price=8999.00"
```

### 2. 库存不足（事务回滚）
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=1&productId=1&count=200&price=8999.00"
```

### 3. 余额不足（事务回滚）
```bash
curl -X POST "http://localhost:8081/order/create" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "userId=2&productId=2&count=5&price=15999.00"
```

## 📊 数据库设计

### 订单表 (seata_order.t_order)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 订单ID |
| user_id | BIGINT | 用户ID |
| product_id | BIGINT | 商品ID |
| count | INT | 商品数量 |
| price | DECIMAL(10,2) | 商品单价 |
| total_amount | DECIMAL(10,2) | 订单总金额 |
| status | INT | 订单状态 |

### 库存表 (seata_stock.t_stock)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 库存ID |
| product_id | BIGINT | 商品ID |
| product_name | VARCHAR(100) | 商品名称 |
| stock | INT | 总库存 |
| used | INT | 已使用库存 |
| residue | INT | 剩余库存 |

### 账户表 (seata_account.t_account)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 账户ID |
| user_id | BIGINT | 用户ID |
| user_name | VARCHAR(50) | 用户名 |
| total | DECIMAL(10,2) | 总余额 |
| used | DECIMAL(10,2) | 已使用金额 |
| residue | DECIMAL(10,2) | 剩余余额 |

## 🔧 配置说明

### Seata配置
每个服务都配置了相同的Seata参数：
```yaml
seata:
  enabled: true
  application-id: {service-name}
  tx-service-group: my_test_tx_group
  service:
    vgroup-mapping:
      my_test_tx_group: default
    grouplist:
      default: 114.132.181.98:8091
  config:
    type: file
  registry:
    type: file
```

### 数据源配置
- **订单服务**: seata_order数据库
- **库存服务**: seata_stock数据库
- **账户服务**: seata_account数据库

## 🎯 关键注解

### @GlobalTransactional
在订单服务的创建订单方法上使用：
```java
@GlobalTransactional(name = "create-order", rollbackFor = Exception.class)
public Long createOrder(Order order) {
    // 分布式事务逻辑
}
```

### @Transactional
在各个服务的业务方法上使用本地事务：
```java
@Transactional(rollbackFor = Exception.class)
public Boolean deductStock(Long productId, Integer count) {
    // 本地事务逻辑
}
```

## 📈 监控和日志

### Seata控制台
- 访问地址: http://114.132.181.98:7091
- 用户名: seata
- 密码: seata

### 应用日志
- 订单服务日志: logs/order-service.log
- 库存服务日志: logs/stock-service.log
- 账户服务日志: logs/account-service.log

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   - 检查MySQL连接配置
   - 确认Seata服务器是否运行
   - 检查端口是否被占用

2. **分布式事务不生效**
   - 确认@GlobalTransactional注解是否正确
   - 检查Seata配置是否一致
   - 查看Seata服务器日志

3. **数据库连接失败**
   - 检查数据库地址和端口
   - 确认用户名密码正确
   - 验证数据库是否已创建

### 日志级别调整
```yaml
logging:
  level:
    com.example: DEBUG
    io.seata: INFO
```

## 🔄 扩展功能

### 1. 添加新的微服务
1. 创建新的Spring Boot模块
2. 添加Seata依赖和配置
3. 在需要参与分布式事务的方法上添加@Transactional
4. 在业务数据库中创建undo_log表

### 2. 集成注册中心
可以集成Nacos作为注册中心：
```yaml
seata:
  registry:
    type: nacos
    nacos:
      server-addr: localhost:8848
```

### 3. 配置中心
可以使用Nacos作为配置中心：
```yaml
seata:
  config:
    type: nacos
    nacos:
      server-addr: localhost:8848
```

## 📝 开发指南

### 新增业务服务步骤
1. 创建Spring Boot项目
2. 添加依赖（Web、MyBatis Plus、Seata等）
3. 配置数据源和Seata
4. 创建业务表和undo_log表
5. 实现业务逻辑，添加事务注解
6. 测试本地事务和分布式事务

### 最佳实践
- 合理设计事务边界，避免长事务
- 使用幂等性设计，避免重复操作
- 监控事务执行情况，及时发现问题
- 定期清理undo_log表中的历史数据

## 📚 项目文档

### 📖 详细文档
- [📋 项目说明](docs/项目说明.md) - 完整的项目介绍和架构说明
- [🚀 部署指南](docs/部署指南.md) - 详细的部署步骤和配置说明
- [📡 API文档](docs/API文档.md) - 完整的API接口文档
- [🧪 测试指南](docs/测试指南.md) - 全面的测试方案和用例
- [🔧 故障排除](docs/故障排除.md) - 常见问题和解决方案

### 🔗 相关资源
- [Seata官方文档](https://seata.io/zh-cn/)
- [Spring Cloud Alibaba](https://spring-cloud-alibaba-group.github.io/github-pages/hoxton/zh-cn/index.html)
- [分布式事务原理](https://seata.io/zh-cn/docs/overview/what-is-seata.html)

## 📞 联系方式

如有问题或建议，请提交Issue或联系开发团队。

---
**项目创建时间**: 2025年7月29日
**Seata版本**: 1.5.2
**Spring Cloud版本**: 2021.0.8
