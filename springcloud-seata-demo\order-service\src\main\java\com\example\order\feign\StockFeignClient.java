package com.example.order.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 库存服务Feign客户端
 */
@FeignClient(name = "stock-service", url = "http://localhost:8082")
public interface StockFeignClient {
    
    /**
     * 扣减库存
     * @param productId 商品ID
     * @param count 扣减数量
     * @return 是否成功
     */
    @PostMapping("/stock/deduct")
    Boolean deductStock(@RequestParam("productId") Long productId, 
                       @RequestParam("count") Integer count);
}
