package com.example.account.service.impl;

import com.example.account.entity.Account;
import com.example.account.mapper.AccountMapper;
import com.example.account.service.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 账户服务实现类
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {
    
    @Autowired
    private AccountMapper accountMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deductBalance(Long userId, BigDecimal amount) {
        log.info("开始扣减账户余额，用户ID：{}，扣减金额：{}", userId, amount);
        
        try {
            int result = accountMapper.deductBalance(userId, amount);
            if (result > 0) {
                log.info("账户余额扣减成功，用户ID：{}，扣减金额：{}", userId, amount);
                return true;
            } else {
                log.warn("账户余额扣减失败，可能余额不足，用户ID：{}，扣减金额：{}", userId, amount);
                return false;
            }
        } catch (Exception e) {
            log.error("账户余额扣减异常，用户ID：{}，扣减金额：{}", userId, amount, e);
            throw new RuntimeException("账户余额扣减失败：" + e.getMessage());
        }
    }

    @Override
    public List<Account> getAllAccounts() {
        return Collections.emptyList();
    }

    @Override
    public void resetData() {

    }
}
