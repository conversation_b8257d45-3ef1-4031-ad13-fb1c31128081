package com.example.stock.controller;

import com.example.stock.entity.Stock;
import com.example.stock.service.StockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存控制器
 */
@Slf4j
@RestController
@RequestMapping("/stock")
public class StockController {
    
    @Autowired
    private StockService stockService;
    
    /**
     * 扣减库存
     */
    @PostMapping("/deduct")
    public Boolean deductStock(@RequestParam Long productId, @RequestParam Integer count) {
        log.info("接收到扣减库存请求：productId={}, count={}", productId, count);
        return stockService.deductStock(productId, count);
    }
    
    /**
     * 查询所有库存
     */
    @GetMapping("/list")
    public Map<String, Object> list() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Stock> stocks = stockService.getAllStocks();
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", stocks);
        } catch (Exception e) {
            log.error("查询库存列表失败：", e);
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 重置库存数据
     */
    @PostMapping("/reset")
    public Map<String, Object> reset() {
        Map<String, Object> result = new HashMap<>();
        try {
            stockService.resetData();
            result.put("success", true);
            result.put("message", "库存数据重置成功");
            log.info("库存数据重置成功");
        } catch (Exception e) {
            log.error("重置库存数据失败：", e);
            result.put("success", false);
            result.put("message", "重置失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("service", "stock-service");
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
