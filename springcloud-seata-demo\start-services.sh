#!/bin/bash

echo "================================"
echo "Spring Cloud Seata Demo 启动脚本"
echo "================================"

echo "正在启动各个微服务..."

echo ""
echo "1. 启动订单服务 (端口: 8081)"
cd order-service
nohup mvn spring-boot:run > ../logs/order-service.log 2>&1 &
ORDER_PID=$!
echo "订单服务 PID: $ORDER_PID"
cd ..

echo ""
echo "等待5秒..."
sleep 5

echo "2. 启动库存服务 (端口: 8082)"
cd stock-service
nohup mvn spring-boot:run > ../logs/stock-service.log 2>&1 &
STOCK_PID=$!
echo "库存服务 PID: $STOCK_PID"
cd ..

echo ""
echo "等待5秒..."
sleep 5

echo "3. 启动账户服务 (端口: 8083)"
cd account-service
nohup mvn spring-boot:run > ../logs/account-service.log 2>&1 &
ACCOUNT_PID=$!
echo "账户服务 PID: $ACCOUNT_PID"
cd ..

echo ""
echo "================================"
echo "所有服务启动完成！"
echo "================================"
echo "订单服务: http://localhost:8081"
echo "库存服务: http://localhost:8082"
echo "账户服务: http://localhost:8083"
echo "Seata控制台: http://**************:7091"
echo "================================"
echo "服务进程ID:"
echo "订单服务: $ORDER_PID"
echo "库存服务: $STOCK_PID"
echo "账户服务: $ACCOUNT_PID"
echo "================================"

# 创建停止脚本
cat > stop-services.sh << EOF
#!/bin/bash
echo "正在停止所有服务..."
kill $ORDER_PID $STOCK_PID $ACCOUNT_PID 2>/dev/null
echo "所有服务已停止"
EOF

chmod +x stop-services.sh
echo "停止脚本已创建: ./stop-services.sh"
