package com.example.stock.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.stock.entity.Stock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 库存Mapper接口
 */
@Mapper
public interface StockMapper extends BaseMapper<Stock> {
    
    /**
     * 扣减库存
     * @param productId 商品ID
     * @param count 扣减数量
     * @return 影响行数
     */
    @Update("UPDATE t_stock SET used = used + #{count}, residue = residue - #{count}, " +
            "update_time = NOW() WHERE product_id = #{productId} AND residue >= #{count}")
    int deductStock(@Param("productId") Long productId, @Param("count") Integer count);

    /**
     * 重置所有库存数据
     * @return 影响行数
     */
    @Update("UPDATE t_stock SET used = 0, residue = total, update_time = NOW()")
    int resetAllStock();
}
