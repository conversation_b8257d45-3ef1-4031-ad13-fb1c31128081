package com.example.order.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

/**
 * 账户服务Feign客户端
 */
@FeignClient(name = "account-service", url = "http://localhost:8083")
public interface AccountFeignClient {
    
    /**
     * 扣减账户余额
     * @param userId 用户ID
     * @param amount 扣减金额
     * @return 是否成功
     */
    @PostMapping("/account/deduct")
    Boolean deductBalance(@RequestParam("userId") Long userId, 
                         @RequestParam("amount") BigDecimal amount);
}
