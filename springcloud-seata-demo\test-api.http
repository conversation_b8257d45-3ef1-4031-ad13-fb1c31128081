### Spring Cloud Seata Demo 测试接口

### 1. 健康检查 - 订单服务
GET http://localhost:8081/order/health

### 2. 健康检查 - 库存服务
GET http://localhost:8082/stock/health

### 3. 健康检查 - 账户服务
GET http://localhost:8083/account/health

### 4. 创建订单 - 正常情况（事务成功）
POST http://localhost:8081/order/create
Content-Type: application/x-www-form-urlencoded

userId=1&productId=1&count=2&price=8999.00

### 5. 创建订单 - 库存不足（事务回滚）
POST http://localhost:8081/order/create
Content-Type: application/x-www-form-urlencoded

userId=1&productId=1&count=200&price=8999.00

### 6. 创建订单 - 余额不足（事务回滚）
POST http://localhost:8081/order/create
Content-Type: application/x-www-form-urlencoded

userId=2&productId=2&count=5&price=15999.00

### 7. 单独测试库存扣减
POST http://localhost:8082/stock/deduct
Content-Type: application/x-www-form-urlencoded

productId=1&count=1

### 8. 单独测试账户扣减
POST http://localhost:8083/account/deduct
Content-Type: application/x-www-form-urlencoded

userId=1&amount=100.00

### 9. 批量测试 - 多个订单
POST http://localhost:8081/order/create
Content-Type: application/x-www-form-urlencoded

userId=3&productId=3&count=1&price=3999.00

###
POST http://localhost:8081/order/create
Content-Type: application/x-www-form-urlencoded

userId=3&productId=3&count=2&price=3999.00

### 10. 压力测试 - 并发订单（可以用工具发送多次）
POST http://localhost:8081/order/create
Content-Type: application/x-www-form-urlencoded

userId=1&productId=1&count=1&price=8999.00
