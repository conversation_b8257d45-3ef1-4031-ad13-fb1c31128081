package com.example.stock.service;

import com.example.stock.entity.Stock;
import java.util.List;

/**
 * 库存服务接口
 */
public interface StockService {
    
    /**
     * 扣减库存
     * @param productId 商品ID
     * @param count 扣减数量
     * @return 是否成功
     */
    Boolean deductStock(Long productId, Integer count);

    /**
     * 查询所有库存
     * @return 库存列表
     */
    List<Stock> getAllStocks();

    /**
     * 重置库存数据
     */
    void resetData();
}
