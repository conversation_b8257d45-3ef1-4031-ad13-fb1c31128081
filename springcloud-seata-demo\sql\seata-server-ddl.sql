-- ================================
-- Seata Server 数据库DDL脚本
-- 适用于 Seata 1.5.2 版本
-- 数据库地址: 121.37.42.37:32775
-- ================================

-- 创建Seata管理数据库
CREATE DATABASE IF NOT EXISTS seata DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE seata;

-- ================================
-- 1. 全局事务表 (global_table)
-- 用于存储全局事务信息
-- ================================
DROP TABLE IF EXISTS global_table;
CREATE TABLE global_table (
    xid VARCHAR(128) NOT NULL COMMENT '全局事务ID',
    transaction_id BIGINT COMMENT '事务ID',
    status TINYINT NOT NULL COMMENT '事务状态：1-开始，2-提交中，3-提交失败，4-回滚中，5-回滚失败，6-已提交，7-已回滚，8-超时回滚，9-超时提交',
    application_id VARCHAR(32) COMMENT '应用ID',
    transaction_service_group VARCHAR(32) COMMENT '事务服务组',
    transaction_name VARCHAR(128) COMMENT '事务名称',
    timeout INT COMMENT '超时时间(毫秒)',
    begin_time BIGINT COMMENT '开始时间',
    application_data VARCHAR(2000) COMMENT '应用数据',
    gmt_create DATETIME COMMENT '创建时间',
    gmt_modified DATETIME COMMENT '修改时间',
    PRIMARY KEY (xid),
    KEY idx_status_gmt_modified (status, gmt_modified),
    KEY idx_transaction_id (transaction_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局事务表';

-- ================================
-- 2. 分支事务表 (branch_table)
-- 用于存储分支事务信息
-- ================================
DROP TABLE IF EXISTS branch_table;
CREATE TABLE branch_table (
    branch_id BIGINT NOT NULL COMMENT '分支事务ID',
    xid VARCHAR(128) NOT NULL COMMENT '全局事务ID',
    transaction_id BIGINT COMMENT '事务ID',
    resource_group_id VARCHAR(32) COMMENT '资源组ID',
    resource_id VARCHAR(256) COMMENT '资源ID',
    branch_type VARCHAR(8) COMMENT '分支类型：AT、TCC、SAGA、XA',
    status TINYINT COMMENT '分支状态：1-已注册，2-阶段1完成，3-阶段1失败，4-阶段2完成，5-阶段2失败',
    client_id VARCHAR(64) COMMENT '客户端ID',
    application_data VARCHAR(2000) COMMENT '应用数据',
    gmt_create DATETIME(6) COMMENT '创建时间',
    gmt_modified DATETIME(6) COMMENT '修改时间',
    PRIMARY KEY (branch_id),
    KEY idx_xid (xid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分支事务表';

-- ================================
-- 3. 全局锁表 (lock_table)
-- 用于存储全局锁信息
-- ================================
DROP TABLE IF EXISTS lock_table;
CREATE TABLE lock_table (
    row_key VARCHAR(128) NOT NULL COMMENT '行锁键',
    xid VARCHAR(128) COMMENT '全局事务ID',
    transaction_id BIGINT COMMENT '事务ID',
    branch_id BIGINT NOT NULL COMMENT '分支事务ID',
    resource_id VARCHAR(256) COMMENT '资源ID',
    table_name VARCHAR(32) COMMENT '表名',
    pk VARCHAR(36) COMMENT '主键值',
    row_value MEDIUMTEXT COMMENT '行数据',
    gmt_create DATETIME COMMENT '创建时间',
    gmt_modified DATETIME COMMENT '修改时间',
    PRIMARY KEY (row_key),
    KEY idx_branch_id (branch_id),
    KEY idx_xid_and_branch_id (xid, branch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局锁表';

-- ================================
-- 4. 分布式锁表 (distributed_lock)
-- 用于Seata内部分布式锁
-- ================================
DROP TABLE IF EXISTS distributed_lock;
CREATE TABLE distributed_lock (
    lock_key VARCHAR(20) NOT NULL COMMENT '锁键',
    lock_value VARCHAR(20) NOT NULL COMMENT '锁值',
    expire BIGINT COMMENT '过期时间',
    PRIMARY KEY (lock_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分布式锁表';

-- ================================
-- 插入初始数据
-- ================================

-- 插入分布式锁初始数据
INSERT INTO distributed_lock (lock_key, lock_value, expire) VALUES 
('AsyncCommitting', ' ', 0),
('RetryCommitting', ' ', 0),
('RetryRollbacking', ' ', 0),
('TxTimeoutCheck', ' ', 0)
ON DUPLICATE KEY UPDATE lock_value = VALUES(lock_value);

-- ================================
-- 创建索引优化查询性能
-- ================================

-- 为global_table创建额外索引
CREATE INDEX idx_gmt_modified_status ON global_table (gmt_modified, status);
CREATE INDEX idx_application_id ON global_table (application_id);
CREATE INDEX idx_transaction_service_group ON global_table (transaction_service_group);

-- 为branch_table创建额外索引
CREATE INDEX idx_transaction_id ON branch_table (transaction_id);
CREATE INDEX idx_resource_id ON branch_table (resource_id);
CREATE INDEX idx_gmt_create ON branch_table (gmt_create);

-- 为lock_table创建额外索引
CREATE INDEX idx_table_name ON lock_table (table_name);
CREATE INDEX idx_pk ON lock_table (pk);
CREATE INDEX idx_gmt_create ON lock_table (gmt_create);

-- ================================
-- 显示创建的表
-- ================================
SHOW TABLES;

-- 显示表结构
DESCRIBE global_table;
DESCRIBE branch_table;
DESCRIBE lock_table;
DESCRIBE distributed_lock;
