#!/bin/bash

echo "================================"
echo "Spring Cloud Seata Demo Web界面"
echo "================================"

echo "正在启动Web服务器..."
echo

# 检查Python是否可用
if command -v python3 &> /dev/null; then
    echo "使用Python3启动HTTP服务器..."
    echo "访问地址: http://localhost:8080"
    echo "按 Ctrl+C 停止服务器"
    echo
    python3 -m http.server 8080
elif command -v python &> /dev/null; then
    echo "使用Python启动HTTP服务器..."
    echo "访问地址: http://localhost:8080"
    echo "按 Ctrl+C 停止服务器"
    echo
    python -m http.server 8080
elif command -v node &> /dev/null; then
    echo "使用Node.js启动HTTP服务器..."
    echo "访问地址: http://localhost:8080"
    echo "按 Ctrl+C 停止服务器"
    echo
    npx http-server -p 8080
else
    echo
    echo "错误: 未找到Python或Node.js"
    echo
    echo "请安装以下任一工具："
    echo "1. Python 3.x: https://www.python.org/downloads/"
    echo "2. Node.js: https://nodejs.org/"
    echo
    echo "或者直接用浏览器打开 index.html 文件"
    echo
fi
