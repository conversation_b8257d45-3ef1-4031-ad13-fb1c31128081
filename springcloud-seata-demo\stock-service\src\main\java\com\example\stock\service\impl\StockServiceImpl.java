package com.example.stock.service.impl;

import com.example.stock.entity.Stock;
import com.example.stock.mapper.StockMapper;
import com.example.stock.service.StockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 库存服务实现类
 */
@Slf4j
@Service
public class StockServiceImpl implements StockService {
    
    @Autowired
    private StockMapper stockMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deductStock(Long productId, Integer count) {
        log.info("开始扣减库存，商品ID：{}，扣减数量：{}", productId, count);
        
        try {
            int result = stockMapper.deductStock(productId, count);
            if (result > 0) {
                log.info("库存扣减成功，商品ID：{}，扣减数量：{}", productId, count);
                return true;
            } else {
                log.warn("库存扣减失败，可能库存不足，商品ID：{}，扣减数量：{}", productId, count);
                return false;
            }
        } catch (Exception e) {
            log.error("库存扣减异常，商品ID：{}，扣减数量：{}", productId, count, e);
            throw new RuntimeException("库存扣减失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有库存
     */
    @Override
    public List<Stock> getAllStocks() {
        log.info("查询所有库存");
        return stockMapper.selectList(null);
    }

    /**
     * 重置库存数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetData() {
        log.info("开始重置库存数据");
        // 重置所有库存的used为0，residue为total
        stockMapper.resetAllStock();
        log.info("库存数据重置完成");
    }
}
