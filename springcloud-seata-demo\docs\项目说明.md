# Spring Cloud Seata 分布式事务演示项目

## 📖 项目概述

本项目是一个完整的Spring Cloud分布式事务演示系统，使用Seata实现分布式事务管理。通过电商订单场景展示了分布式事务的一致性保证和回滚机制。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   订单服务       │    │   库存服务       │    │   账户服务       │
│  Order Service  │    │  Stock Service  │    │ Account Service │
│   Port: 8081    │    │   Port: 8082    │    │   Port: 8083    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Seata 服务器   │
                    │  Port: 8091     │
                    │  Console: 7091  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL 数据库   │
                    │ 121.37.42.37    │
                    │   Port: 32775   │
                    └─────────────────┘
```

## 🎯 业务场景

### 订单创建流程
1. **创建订单**：在订单表中插入订单记录
2. **扣减库存**：调用库存服务减少商品库存
3. **扣减余额**：调用账户服务减少用户余额
4. **更新状态**：将订单状态更新为已完成

### 分布式事务保证
- 使用Seata的AT模式实现分布式事务
- 任何一个步骤失败，整个事务自动回滚
- 保证数据的最终一致性

## 🛠️ 技术栈

### 后端技术
- **Spring Boot**: 2.7.18
- **Spring Cloud**: 2021.0.8
- **Seata**: 1.5.2
- **MyBatis Plus**: *******
- **MySQL**: 8.0+
- **OpenFeign**: 服务间调用

### 前端技术
- **HTML5**: 页面结构
- **CSS3**: 样式设计（渐变、动画、响应式）
- **JavaScript**: 交互逻辑（ES6+、Fetch API）
- **响应式设计**: 支持移动端

## 📊 数据库设计

### 订单数据库 (seata_order)
```sql
-- 订单表
CREATE TABLE t_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    count INT NOT NULL,
    money DECIMAL(10,2) NOT NULL,
    status INT DEFAULT 0,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Seata AT模式回滚日志表
CREATE TABLE undo_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL
);
```

### 库存数据库 (seata_stock)
```sql
-- 库存表
CREATE TABLE t_stock (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    total INT NOT NULL,
    used INT DEFAULT 0,
    residue INT NOT NULL
);

-- Seata AT模式回滚日志表
CREATE TABLE undo_log (
    -- 同上
);
```

### 账户数据库 (seata_account)
```sql
-- 账户表
CREATE TABLE t_account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    user_name VARCHAR(50) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    used DECIMAL(10,2) DEFAULT 0,
    residue DECIMAL(10,2) NOT NULL
);

-- Seata AT模式回滚日志表
CREATE TABLE undo_log (
    -- 同上
);
```

### Seata管理数据库 (seata)
```sql
-- 全局事务表
CREATE TABLE global_table (
    xid VARCHAR(128) PRIMARY KEY,
    transaction_id BIGINT,
    status TINYINT NOT NULL,
    application_id VARCHAR(32),
    transaction_service_group VARCHAR(32),
    transaction_name VARCHAR(128),
    timeout INT,
    begin_time BIGINT,
    application_data VARCHAR(2000),
    gmt_create DATETIME,
    gmt_modified DATETIME
);

-- 分支事务表
CREATE TABLE branch_table (
    branch_id BIGINT PRIMARY KEY,
    xid VARCHAR(128) NOT NULL,
    transaction_id BIGINT,
    resource_group_id VARCHAR(32),
    resource_id VARCHAR(256),
    branch_type VARCHAR(8),
    status TINYINT,
    client_id VARCHAR(64),
    application_data VARCHAR(2000),
    gmt_create DATETIME(6),
    gmt_modified DATETIME(6)
);

-- 全局锁表
CREATE TABLE lock_table (
    row_key VARCHAR(128) PRIMARY KEY,
    xid VARCHAR(128),
    transaction_id BIGINT,
    branch_id BIGINT NOT NULL,
    resource_id VARCHAR(256),
    table_name VARCHAR(32),
    pk VARCHAR(36),
    row_value MEDIUMTEXT,
    gmt_create DATETIME,
    gmt_modified DATETIME,
    status TINYINT DEFAULT 0
);

-- 分布式锁表
CREATE TABLE distributed_lock (
    lock_key VARCHAR(20) PRIMARY KEY,
    lock_value VARCHAR(20) NOT NULL,
    expire BIGINT
);
```

## 🔧 配置说明

### Seata配置
每个微服务都包含以下Seata配置：

```yaml
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: my_test_tx_group
  enable-auto-data-source-proxy: true
  data-source-proxy-mode: AT
  service:
    vgroup-mapping:
      my_test_tx_group: default
    grouplist:
      default: 114.132.181.98:8091
  config:
    type: file
  registry:
    type: file
```

### 数据源配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************
    username: root
    password: 123456
```

### CORS配置
为了支持前端跨域访问，每个服务都配置了CORS：

```java
@Configuration
public class CorsConfig {
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        config.setAllowCredentials(true);
        config.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
```

## 📁 项目结构

```
springcloud-seata-demo/
├── order-service/              # 订单服务
│   ├── src/main/java/
│   │   └── com/example/order/
│   │       ├── OrderServiceApplication.java
│   │       ├── controller/OrderController.java
│   │       ├── service/OrderService.java
│   │       ├── mapper/OrderMapper.java
│   │       ├── entity/Order.java
│   │       ├── feign/StockFeignClient.java
│   │       ├── feign/AccountFeignClient.java
│   │       └── config/CorsConfig.java
│   └── src/main/resources/
│       └── application.yml
├── stock-service/              # 库存服务
│   ├── src/main/java/
│   │   └── com/example/stock/
│   │       ├── StockServiceApplication.java
│   │       ├── controller/StockController.java
│   │       ├── service/StockService.java
│   │       ├── mapper/StockMapper.java
│   │       ├── entity/Stock.java
│   │       └── config/CorsConfig.java
│   └── src/main/resources/
│       └── application.yml
├── account-service/            # 账户服务
│   ├── src/main/java/
│   │   └── com/example/account/
│   │       ├── AccountServiceApplication.java
│   │       ├── controller/AccountController.java
│   │       ├── service/AccountService.java
│   │       ├── mapper/AccountMapper.java
│   │       ├── entity/Account.java
│   │       └── config/CorsConfig.java
│   └── src/main/resources/
│       └── application.yml
├── sql/                        # 数据库脚本
│   ├── init.sql               # 业务数据库初始化
│   └── seata-server-ddl.sql   # Seata管理表
├── web/                        # 前端页面
│   ├── index.html             # 主页面
│   ├── start-web.sh           # Linux/Mac启动脚本
│   ├── start-web.bat          # Windows启动脚本
│   └── README.md              # Web界面说明
├── docs/                       # 文档目录
│   ├── 项目说明.md             # 本文档
│   ├── 部署指南.md             # 部署说明
│   ├── API文档.md              # API接口文档
│   └── 故障排除.md             # 常见问题
├── start-services.sh           # Linux/Mac服务启动脚本
├── start-services.bat          # Windows服务启动脚本
├── test-api.http              # API测试文件
└── README.md                  # 项目主文档
```

## 🎨 Web界面功能

### 服务状态监控
- 实时检查三个微服务的运行状态
- 显示服务响应时间和在线状态
- 支持一键刷新所有服务状态

### 数据状态查看
- 查看库存数据（商品库存情况）
- 查看账户数据（用户余额情况）
- 查看订单数据（历史订单记录）
- 支持数据重置功能

### 分布式事务测试
- **自定义订单**：手动选择用户、商品、数量、价格
- **快速测试场景**：
  - ✅ 正常订单：验证事务成功提交
  - ❌ 库存不足：验证事务自动回滚
  - 💰 余额不足：验证事务自动回滚
  - ⚡ 并发测试：验证事务隔离性

### 结果展示
- 详细的操作结果显示
- 事务状态可视化
- 错误信息友好提示
- 实时数据更新

## 🔗 相关链接

- [Seata官方文档](https://seata.io/zh-cn/)
- [Spring Cloud Alibaba](https://spring-cloud-alibaba-group.github.io/github-pages/hoxton/zh-cn/index.html)
- [分布式事务原理](https://seata.io/zh-cn/docs/overview/what-is-seata.html)
- [MyBatis Plus官方文档](https://baomidou.com/)

## 📝 版本信息

- **项目版本**: 1.0.0
- **创建日期**: 2025-07-29
- **最后更新**: 2025-07-29
- **作者**: Augment Agent
- **许可证**: MIT License
