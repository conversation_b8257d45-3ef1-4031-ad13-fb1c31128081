-- ================================
-- Spring Cloud Seata Demo 数据库初始化脚本
-- 数据库地址: 121.37.42.37:32775
-- ================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS seata_order DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS seata_stock DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS seata_account DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ================================
-- 订单服务数据库
-- ================================
USE seata_order;

-- 订单表
DROP TABLE IF EXISTS t_order;
CREATE TABLE t_order (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    count INT NOT NULL COMMENT '商品数量',
    price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    status INT NOT NULL DEFAULT 0 COMMENT '订单状态：0-创建中，1-已完成，2-已取消',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- ================================
-- 库存服务数据库
-- ================================
USE seata_stock;

-- 库存表
DROP TABLE IF EXISTS t_stock;
CREATE TABLE t_stock (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '库存ID',
    product_id BIGINT NOT NULL UNIQUE COMMENT '商品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    stock INT NOT NULL COMMENT '总库存',
    used INT NOT NULL DEFAULT 0 COMMENT '已使用库存',
    residue INT NOT NULL COMMENT '剩余库存',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表';

-- 初始化库存数据
INSERT INTO t_stock (product_id, product_name, stock, residue) VALUES 
(1, 'iPhone 15 Pro', 100, 100),
(2, 'MacBook Pro', 50, 50),
(3, 'iPad Air', 200, 200);

-- ================================
-- 账户服务数据库
-- ================================
USE seata_account;

-- 账户表
DROP TABLE IF EXISTS t_account;
CREATE TABLE t_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    user_name VARCHAR(50) NOT NULL COMMENT '用户名',
    total DECIMAL(10,2) NOT NULL COMMENT '总余额',
    used DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '已使用金额',
    residue DECIMAL(10,2) NOT NULL COMMENT '剩余可用余额',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账户表';

-- 初始化账户数据
INSERT INTO t_account (user_id, user_name, total, residue) VALUES 
(1, '张三', 10000.00, 10000.00),
(2, '李四', 5000.00, 5000.00),
(3, '王五', 8000.00, 8000.00);

-- ================================
-- Seata undo_log表（每个业务数据库都需要）
-- ================================

-- 订单服务undo_log表
USE seata_order;
DROP TABLE IF EXISTS undo_log;
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    ext VARCHAR(100),
    UNIQUE KEY ux_undo_log (xid, branch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 库存服务undo_log表
USE seata_stock;
DROP TABLE IF EXISTS undo_log;
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    ext VARCHAR(100),
    UNIQUE KEY ux_undo_log (xid, branch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 账户服务undo_log表
USE seata_account;
DROP TABLE IF EXISTS undo_log;
CREATE TABLE undo_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT NOT NULL,
    xid VARCHAR(100) NOT NULL,
    context VARCHAR(128) NOT NULL,
    rollback_info LONGBLOB NOT NULL,
    log_status INT NOT NULL,
    log_created DATETIME NOT NULL,
    log_modified DATETIME NOT NULL,
    ext VARCHAR(100),
    UNIQUE KEY ux_undo_log (xid, branch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
