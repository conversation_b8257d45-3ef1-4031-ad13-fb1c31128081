package com.example.account.service;

import com.example.account.entity.Account;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账户服务接口
 */
public interface AccountService {
    
    /**
     * 扣减账户余额
     * @param userId 用户ID
     * @param amount 扣减金额
     * @return 是否成功
     */
    Boolean deductBalance(Long userId, BigDecimal amount);

    /**
     * 查询所有账户
     * @return 账户列表
     */
    List<Account> getAllAccounts();

    /**
     * 重置账户数据
     */
    void resetData();
}
